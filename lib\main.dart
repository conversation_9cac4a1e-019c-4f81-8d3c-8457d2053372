import 'package:refreshed/refreshed.dart';
import 'package:crud/controllers/initial_controller.dart';
import 'package:crud/views/home_view.dart';
import 'package:shadcn_flutter/shadcn_flutter.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

void main() {
  final initialController = Get.put(InitialController());
  runApp(
    ScreenUtilInit(
      designSize: const Size(500, 1200),
      minTextAdapt: true,
      splitScreenMode: true,
      // Use builder only if you need to use library outside ScreenUtilInit context
      builder: (_, child) {
        return Obx(
          () => ShadcnApp(
            title: 'Crud App',
            home: MainApp(),
            theme: ThemeData(
              colorScheme: initialController.checkTheme(),
              radius: 0.5,
            ),
          ),
        );
      },
    ),
  );
}

class MainApp extends StatelessWidget {
  const MainApp({super.key});

  @override
  Widget build(BuildContext context) {
    return HomeView();
  }
}
